#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演员列表获取脚本
使用 javdbid.py 中的已有函数获取演员列表
"""

import re
import urllib.parse
from typing import Set, List
from javdbid import init_driver, get_page, close_driver
import argparse


def extract_actor_urls(soup) -> Set[str]:
    """
    从页面中提取演员页面的URL
    格式：https://javdb.com/actors/****
    """
    actor_urls = set()
    
    if soup is None:
        return actor_urls
    
    # 查找所有链接
    links = soup.find_all('a', href=True)
    
    for link in links:
        href = link.get('href', '')
        
        # 检查是否是演员页面链接
        if '/actors/' in href:
            # 构建完整URL
            if href.startswith('/'):
                full_url = 'https://javdb.com' + href
            elif href.startswith('http'):
                full_url = href
            else:
                continue
            
            # 验证URL格式是否正确
            if re.match(r'https://javdb\.com/actors/[^/]+/?$', full_url):
                # 移除末尾的斜杠以统一格式
                clean_url = full_url.rstrip('/')
                actor_urls.add(clean_url)
    
    return actor_urls


def get_actors_from_collection_page(url: str = "https://javdb.com/users/collection_actors") -> List[str]:
    """
    从演员收藏页面获取所有演员链接
    
    Args:
        url: 演员收藏页面的URL
        
    Returns:
        去重后的演员URL列表
    """
    print(f"正在访问演员收藏页面: {url}")
    
    all_actor_urls = set()
    current_url = url
    page_count = 0
    
    while current_url:
        page_count += 1
        print(f"正在处理第 {page_count} 页...")
        
        # 获取页面内容
        soup = get_page(current_url)
        if soup is None:
            print(f"无法获取页面内容: {current_url}")
            break
        
        # 提取当前页面的演员URL
        page_actor_urls = extract_actor_urls(soup)
        print(f"第 {page_count} 页找到 {len(page_actor_urls)} 个演员链接")
        
        # 添加到总集合中
        all_actor_urls.update(page_actor_urls)
        
        # 查找下一页链接
        next_page_url = get_next_page_url(soup, current_url)
        if next_page_url:
            current_url = next_page_url
            print(f"找到下一页: {next_page_url}")
        else:
            print("没有找到下一页，处理完成")
            break
    
    # 转换为排序后的列表
    sorted_urls = sorted(list(all_actor_urls))
    print(f"总共找到 {len(sorted_urls)} 个唯一的演员链接")
    
    return sorted_urls


def get_next_page_url(soup, current_url: str) -> str:
    """
    获取下一页的URL（复用javdbid.py中的逻辑）
    """
    if soup is None:
        return None
        
    # 查找下一页链接
    next_page_tag = soup.find('a', rel='next', class_='pagination-next')
    if next_page_tag and next_page_tag.get('href'):
        next_page_url = urllib.parse.urljoin(current_url, next_page_tag['href'])
        return next_page_url
    
    # 如果没有找到标准的下一页链接，尝试其他可能的选择器
    pagination_links = soup.find_all('a', class_='pagination-link')
    for link in pagination_links:
        if '下一页' in link.get_text() or 'Next' in link.get_text():
            href = link.get('href')
            if href:
                return urllib.parse.urljoin(current_url, href)
    
    return None


def save_actor_urls_to_file(actor_urls: List[str], filename: str = "actor_urls.txt"):
    """
    将演员URL列表保存到文件
    """
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            for url in actor_urls:
                f.write(url + '\n')
        print(f"演员URL列表已保存到文件: {filename}")
    except Exception as e:
        print(f"保存文件时出错: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="获取JAVDB演员列表")
    parser.add_argument("--url", 
                       default="https://javdb.com/users/collection_actors",
                       help="演员收藏页面URL")
    parser.add_argument("--output", "-o",
                       default="actor_urls.txt",
                       help="输出文件名")
    parser.add_argument("--headless", 
                       action="store_true",
                       help="使用无头浏览器模式")
    
    args = parser.parse_args()
    
    try:
        # 初始化浏览器驱动
        print("正在初始化浏览器...")
        init_driver(headless=args.headless)
        
        # 获取演员URL列表
        actor_urls = get_actors_from_collection_page(args.url)
        
        if actor_urls:
            # 保存到文件
            save_actor_urls_to_file(actor_urls, args.output)
            
            # 显示前10个URL作为示例
            print("\n前10个演员URL示例:")
            for i, url in enumerate(actor_urls[:10], 1):
                print(f"{i:2d}. {url}")
            
            if len(actor_urls) > 10:
                print(f"... 还有 {len(actor_urls) - 10} 个URL")
        else:
            print("没有找到任何演员URL")
    
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"程序执行出错: {e}")
    finally:
        # 关闭浏览器驱动
        print("正在关闭浏览器...")
        close_driver()


if __name__ == "__main__":
    main()
