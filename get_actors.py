#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演员列表获取脚本
使用 javdbid.py 中的已有函数获取演员列表
"""

import re
import urllib.parse
import os
import platform
from typing import Set, List, Optional
from javdbid import get_page, close_driver
import undetected_chromedriver as uc
from selenium.webdriver.chrome.options import Options as ChromeOptions
import argparse


def init_driver_with_temp_profile_copy(headless: bool = False, profile_name: str = "Profile 5"):
    """
    创建临时profile并复制现有profile的重要数据

    Args:
        headless: 是否使用无头模式
        profile_name: 要复制的Chrome profile名称
    """
    import javdbid
    import shutil
    import tempfile

    options = ChromeOptions()
    options.add_argument("--disable-gpu")
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-blink-features=AutomationControlled")
    options.add_argument("--disable-extensions")
    options.add_argument("--no-first-run")
    options.add_argument("--no-default-browser-check")
    options.add_argument("--disable-infobars")
    options.add_argument("--disable-dev-shm-usage")

    # 获取系统默认的Chrome用户数据目录
    system = platform.system()
    if system == "Windows":
        user_data_dir = os.path.expanduser(r"~\AppData\Local\Google\Chrome\User Data")
    elif system == "Darwin":  # macOS
        user_data_dir = os.path.expanduser("~/Library/Application Support/Google/Chrome")
    else:  # Linux
        user_data_dir = os.path.expanduser("~/.config/google-chrome")

    # 创建临时用户数据目录
    temp_user_data = tempfile.mkdtemp(prefix="chrome_temp_")
    temp_profile_dir = os.path.join(temp_user_data, "Default")
    os.makedirs(temp_profile_dir, exist_ok=True)

    # 复制重要的profile文件（如cookies）
    source_profile_dir = os.path.join(user_data_dir, profile_name)
    if os.path.exists(source_profile_dir):
        important_files = ["Cookies", "Login Data", "Preferences", "Local State"]
        for file_name in important_files:
            source_file = os.path.join(source_profile_dir, file_name)
            if os.path.exists(source_file):
                try:
                    dest_file = os.path.join(temp_profile_dir, file_name)
                    shutil.copy2(source_file, dest_file)
                    print(f"已复制 {file_name}")
                except Exception as e:
                    print(f"复制 {file_name} 失败: {e}")

    # 设置临时用户数据目录
    options.add_argument(f"--user-data-dir={temp_user_data}")

    if headless:
        options.add_argument("--headless=new")

    try:
        print("正在启动Chrome浏览器（临时profile）...")
        javdbid.driver = uc.Chrome(options=options)
        print(f"已使用临时profile初始化浏览器（基于 {profile_name}）")

        # 设置页面加载超时
        javdbid.driver.set_page_load_timeout(30)
        javdbid.driver.implicitly_wait(10)

        print("浏览器初始化完成")

    except Exception as e:
        print(f"初始化浏览器时出错: {e}")
        # 清理临时目录
        try:
            shutil.rmtree(temp_user_data)
        except:
            pass
        raise


def init_driver_with_profile(headless: bool = False, profile_name: str = "Profile 5"):
    """
    使用指定的Chrome profile初始化浏览器驱动

    Args:
        headless: 是否使用无头模式
        profile_name: Chrome profile名称，默认为"Profile 5"
    """
    # 导入全局driver变量
    import javdbid

    options = ChromeOptions()
    options.add_argument("--disable-gpu")
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-blink-features=AutomationControlled")

    # 添加更多稳定性参数
    options.add_argument("--disable-extensions")
    options.add_argument("--disable-plugins")
    options.add_argument("--no-first-run")
    options.add_argument("--no-default-browser-check")
    options.add_argument("--disable-default-apps")
    options.add_argument("--disable-popup-blocking")
    options.add_argument("--disable-infobars")
    options.add_argument("--disable-dev-shm-usage")

    # 获取系统默认的Chrome用户数据目录
    system = platform.system()
    if system == "Windows":
        user_data_dir = os.path.expanduser(r"~\AppData\Local\Google\Chrome\User Data")
    elif system == "Darwin":  # macOS
        user_data_dir = os.path.expanduser("~/Library/Application Support/Google/Chrome")
    else:  # Linux
        user_data_dir = os.path.expanduser("~/.config/google-chrome")

    # 设置用户数据目录和profile
    options.add_argument(f"--user-data-dir={user_data_dir}")
    options.add_argument(f"--profile-directory={profile_name}")

    # 添加参数以允许多个Chrome实例使用同一profile
    options.add_argument("--remote-debugging-port=0")  # 使用随机端口
    options.add_argument("--disable-web-security")
    options.add_argument("--allow-running-insecure-content")

    if headless:
        # 兼容新版 Chrome 的无头参数
        options.add_argument("--headless=new")

    try:
        # 初始化driver并设置到javdbid模块的全局变量中
        print("正在启动Chrome浏览器...")
        javdbid.driver = uc.Chrome(options=options)
        print(f"已使用Chrome {profile_name} profile初始化浏览器")

        # 设置页面加载超时
        javdbid.driver.set_page_load_timeout(30)
        javdbid.driver.implicitly_wait(10)

        print("浏览器初始化完成")

    except Exception as e:
        print(f"初始化浏览器时出错: {e}")
        raise


def extract_actor_urls(soup) -> Set[str]:
    """
    从页面中提取演员页面的URL

    Args:
        soup: BeautifulSoup解析后的页面对象

    Returns:
        Set[str]: 去重后的演员URL集合，格式：https://javdb.com/actors/****
    """
    actor_urls = set()

    if soup is None:
        return actor_urls

    # 查找所有链接
    links = soup.find_all('a', href=True)
    print(f"页面中找到 {len(links)} 个链接")

    for link in links:
        href = link.get('href', '')

        # 检查是否是演员页面链接
        if '/actors/' in href:
            # 构建完整URL
            if href.startswith('/'):
                full_url = 'https://javdb.com' + href
            elif href.startswith('http'):
                full_url = href
            else:
                continue

            # 验证URL格式是否正确，排除分类页面
            if re.match(r'https://javdb\.com/actors/[^/]+/?$', full_url):
                # 排除明显的分类链接
                if not any(category in full_url.lower() for category in ['censored', 'uncensored', 'western']):
                    # 移除末尾的斜杠以统一格式
                    clean_url = full_url.rstrip('/')
                    actor_urls.add(clean_url)
                    print(f"找到演员链接: {clean_url}")

    return actor_urls


def check_login_required(soup) -> bool:
    """检查页面是否需要登录"""
    if soup is None:
        return True

    # 检查登录相关的关键词
    page_text = soup.get_text().lower()
    login_indicators = ['登入', 'login', '此內容需要登入', '需要登录']

    for indicator in login_indicators:
        if indicator in page_text:
            return True

    # 检查标题
    title = soup.find('title')
    if title and '登入' in title.get_text():
        return True

    return False


def get_actors_from_any_page(url: str) -> List[str]:
    """
    从任意页面获取所有演员链接

    Args:
        url: 要抓取的页面URL

    Returns:
        去重后的演员URL列表
    """
    print(f"正在访问页面: {url}")

    all_actor_urls = set()
    current_url = url
    page_count = 0

    while current_url:
        page_count += 1
        print(f"正在处理第 {page_count} 页...")

        # 获取页面内容
        try:
            soup = get_page(current_url)
            if soup is None:
                print(f"无法获取页面内容: {current_url}")
                break
        except Exception as e:
            print(f"获取页面时出错: {e}")
            break

        # 检查是否需要登录
        if check_login_required(soup):
            print("⚠️  检测到页面需要登录才能访问")
            print("请确保:")
            print("1. 在指定的Chrome profile中已经登录JAVDB")
            print("2. 或者使用其他不需要登录的页面URL")
            print("3. 例如: https://javdb.com/actors (演员列表页面)")
            break

        # 保存页面HTML用于调试（仅第一页）
        if page_count == 1:
            try:
                with open(f"debug_page_{page_count}.html", 'w', encoding='utf-8') as f:
                    f.write(soup.prettify())
                print(f"已保存第 {page_count} 页HTML到 debug_page_{page_count}.html")
            except Exception as e:
                print(f"保存HTML失败: {e}")

        # 提取当前页面的演员URL
        page_actor_urls = extract_actor_urls(soup)
        print(f"第 {page_count} 页找到 {len(page_actor_urls)} 个演员链接")

        # 添加到总集合中
        all_actor_urls.update(page_actor_urls)

        # 查找下一页链接
        next_page_url = get_next_page_url(soup, current_url)
        if next_page_url:
            current_url = next_page_url
            print(f"找到下一页: {next_page_url}")
        else:
            print("没有找到下一页，处理完成")
            break

    # 转换为排序后的列表
    sorted_urls = sorted(list(all_actor_urls))
    print(f"总共找到 {len(sorted_urls)} 个唯一的演员链接")

    return sorted_urls


def get_next_page_url(soup, current_url: str) -> Optional[str]:
    """
    获取下一页的URL（复用javdbid.py中的逻辑）

    Args:
        soup: BeautifulSoup解析后的页面对象
        current_url: 当前页面的URL

    Returns:
        Optional[str]: 下一页的URL，如果没有下一页则返回None
    """
    if soup is None:
        return None
        
    # 查找下一页链接
    next_page_tag = soup.find('a', rel='next', class_='pagination-next')
    if next_page_tag and next_page_tag.get('href'):
        next_page_url = urllib.parse.urljoin(current_url, next_page_tag['href'])
        return next_page_url
    
    # 如果没有找到标准的下一页链接，尝试其他可能的选择器
    pagination_links = soup.find_all('a', class_='pagination-link')
    for link in pagination_links:
        if '下一页' in link.get_text() or 'Next' in link.get_text():
            href = link.get('href')
            if href:
                return urllib.parse.urljoin(current_url, href)
    
    return None


def save_actor_urls_to_file(actor_urls: List[str], filename: str = "actor_urls.txt"):
    """
    将演员URL列表保存到文件
    """
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            for url in actor_urls:
                f.write(url + '\n')
        print(f"演员URL列表已保存到文件: {filename}")
    except Exception as e:
        print(f"保存文件时出错: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="获取JAVDB演员列表")
    parser.add_argument("--url", 
                       default="https://javdb.com/users/collection_actors",
                       help="演员收藏页面URL")
    parser.add_argument("--output", "-o",
                       default="actor_urls.txt",
                       help="输出文件名")
    parser.add_argument("--headless",
                       action="store_true",
                       help="使用无头浏览器模式")
    parser.add_argument("--profile",
                       default="Profile 5",
                       help="Chrome profile名称 (默认: Profile 5)")
    parser.add_argument("--use-temp-profile",
                       action="store_true",
                       help="使用临时profile而不是系统profile")
    
    args = parser.parse_args()
    
    try:
        # 初始化浏览器驱动
        if args.use_temp_profile:
            print("正在初始化浏览器 (使用临时profile)...")
            from javdbid import init_driver
            init_driver(headless=args.headless)
        else:
            print(f"正在初始化浏览器 (复制 {args.profile} profile到临时目录)...")
            try:
                # 使用临时profile复制方法，避免profile冲突
                init_driver_with_temp_profile_copy(headless=args.headless, profile_name=args.profile)
            except Exception as e:
                print(f"使用profile复制方法失败: {e}")
                print("尝试直接使用指定profile...")
                try:
                    init_driver_with_profile(headless=args.headless, profile_name=args.profile)
                except Exception as e2:
                    print(f"直接使用profile也失败: {e2}")
                    print("回退到使用临时profile...")
                    # 回退到javdbid的原始初始化方法
                    from javdbid import init_driver
                    init_driver(headless=args.headless)
        
        # 获取演员URL列表
        actor_urls = get_actors_from_collection_page(args.url)
        
        if actor_urls:
            # 保存到文件
            save_actor_urls_to_file(actor_urls, args.output)
            
            # 显示前10个URL作为示例
            print("\n前10个演员URL示例:")
            for i, url in enumerate(actor_urls[:10], 1):
                print(f"{i:2d}. {url}")
            
            if len(actor_urls) > 10:
                print(f"... 还有 {len(actor_urls) - 10} 个URL")
        else:
            print("没有找到任何演员URL")
    
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"程序执行出错: {e}")
    finally:
        # 关闭浏览器驱动
        print("正在关闭浏览器...")
        close_driver()


if __name__ == "__main__":
    main()
