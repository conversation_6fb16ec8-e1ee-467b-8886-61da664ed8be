#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Chrome profile连接的脚本
"""

import os
import platform
import time
import undetected_chromedriver as uc
from selenium.webdriver.chrome.options import Options as ChromeOptions


def test_chrome_profile(profile_name: str = "Profile 5"):
    """测试Chrome profile是否可以正常使用"""
    
    print(f"测试Chrome {profile_name} profile...")
    
    # 获取系统默认的Chrome用户数据目录
    system = platform.system()
    if system == "Windows":
        user_data_dir = os.path.expanduser(r"~\AppData\Local\Google\Chrome\User Data")
    elif system == "Darwin":  # macOS
        user_data_dir = os.path.expanduser("~/Library/Application Support/Google/Chrome")
    else:  # Linux
        user_data_dir = os.path.expanduser("~/.config/google-chrome")
    
    print(f"Chrome用户数据目录: {user_data_dir}")
    
    # 检查目录是否存在
    if not os.path.exists(user_data_dir):
        print("❌ Chrome用户数据目录不存在")
        return False
    
    profile_dir = os.path.join(user_data_dir, profile_name)
    print(f"Profile目录: {profile_dir}")
    
    if not os.path.exists(profile_dir):
        print(f"❌ Profile {profile_name} 不存在")
        print("可用的profiles:")
        for item in os.listdir(user_data_dir):
            if item.startswith("Profile") or item == "Default":
                print(f"  - {item}")
        return False
    
    print(f"✅ Profile {profile_name} 存在")
    
    # 尝试启动浏览器
    options = ChromeOptions()
    options.add_argument("--disable-gpu")
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-blink-features=AutomationControlled")
    options.add_argument("--disable-extensions")
    options.add_argument("--no-first-run")
    options.add_argument("--no-default-browser-check")
    options.add_argument("--disable-infobars")
    options.add_argument(f"--user-data-dir={user_data_dir}")
    options.add_argument(f"--profile-directory={profile_name}")
    
    driver = None
    try:
        print("正在启动Chrome...")
        driver = uc.Chrome(options=options)
        print("✅ Chrome启动成功")
        
        # 设置超时
        driver.set_page_load_timeout(10)
        
        # 尝试访问一个简单的页面
        print("正在访问测试页面...")
        driver.get("https://www.baidu.com")
        print("✅ 页面访问成功")
        
        time.sleep(2)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
        
    finally:
        if driver:
            try:
                driver.quit()
                print("浏览器已关闭")
            except:
                pass


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="测试Chrome profile")
    parser.add_argument("--profile", default="Profile 5", help="要测试的profile名称")
    
    args = parser.parse_args()
    
    success = test_chrome_profile(args.profile)
    
    if success:
        print("\n✅ 测试通过！可以使用此profile")
    else:
        print("\n❌ 测试失败！建议使用临时profile")
        print("\n建议的解决方案:")
        print("1. 使用 --use-temp-profile 参数")
        print("2. 或者选择其他可用的profile")
        print("3. 或者确保Chrome浏览器完全关闭后再试")
