import time
import re
import pyperclip  # 用于操作剪贴板
import urllib.parse
from concurrent.futures import ThreadPoolExecutor
from searchlocal import get_search_result  # 修改：导入 check_title_existence 函数
import os
import argparse
import undetected_chromedriver as uc
from selenium.webdriver.chrome.options import Options as ChromeOptions
from bs4 import BeautifulSoup  # 新增：导入 BeautifulSoup 模块
import random
from typing import Optional, Tuple, List, Union

# 尝试导入 Pillow 用于展示 JPG 封面（预留用于未来功能）
try:
    from PIL import Image, ImageTk
    _ = Image, ImageTk  # 标记为已使用，避免警告
except Exception:
    Image = None
    ImageTk = None

# 视觉点击依赖（可选）
try:
    import numpy as np
    import cv2
except Exception:
    np = None
    cv2 = None

# OCR功能已移除

# GUI 组件按需导入，便于无头/服务器环境运行
try:
    import tkinter as tk  # 新增：导入 tkinter 模块
    from tkinter import simpledialog, messagebox  # 新增：导入 messagebox 模块
except Exception:
    tk = None
    simpledialog = None
    messagebox = None

# 全局变量，用于保存会话
session = None  # 删除: session = requests.Session()
driver: Optional[uc.Chrome] = None  # 新增：用于保存 Chrome 实例

# 气泡提示相关变量
tooltip_window = None
tooltip_label = None


def show_tooltip(event, text):
    """显示气泡提示"""
    global tooltip_window, tooltip_label
    
    # 隐藏之前的提示
    hide_tooltip()
    
    if not tk:
        return
    
    # 创建提示窗口
    tooltip_window = tk.Toplevel()
    tooltip_window.wm_overrideredirect(True)  # 无边框窗口
    tooltip_window.wm_geometry("+%d+%d" % (event.x_root + 10, event.y_root + 10))
    
    # 创建提示标签
    tooltip_label = tk.Label(tooltip_window, text=text, justify=tk.LEFT,
                           background="#ffffe0", relief=tk.SOLID, borderwidth=1,
                           wraplength=300, padx=5, pady=3)
    tooltip_label.pack()
    
    # 设置窗口置顶
    tooltip_window.attributes('-topmost', True)


def hide_tooltip():
    """隐藏气泡提示"""
    global tooltip_window, tooltip_label
    
    if tooltip_window:
        tooltip_window.destroy()
        tooltip_window = None
        tooltip_label = None


def init_driver(headless: bool = False):
    global driver
    options = ChromeOptions()
    options.add_argument("--disable-gpu")
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-blink-features=AutomationControlled")
    # 新增：创建 temp 文件夹用于保存 Chrome 用户数据
    temp_dir = os.path.join(os.getcwd(), 'temp')
    if not os.path.exists(temp_dir):
        os.makedirs(temp_dir)
    # 启动前：检测并清理过大的 Preferences，避免 Chrome 启动失败
    prelaunch_guard_preferences(temp_dir)
    options.add_argument(f'--user-data-dir={temp_dir}')
    # 可选：设置下载目录（需用prefs）
    prefs = {
        "download.default_directory": temp_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    options.add_experimental_option("prefs", prefs)
    if headless:
        # 兼容新版 Chrome 的无头参数
        options.add_argument("--headless=new")
    driver = uc.Chrome(options=options)
    print(driver.capabilities.get("chrome", {}))


def close_driver():
    global driver
    try:
        if driver:
            # 在关闭浏览器前先清理缓存
            cleanup_chrome_cache()
            driver.quit()  # 关闭 Chrome 实例
    finally:
        pass


def cleanup_chrome_cache():
    """使用Chrome自己的清理功能清理缓存，保留重要信息"""
    global driver
    if not driver:
        return
    
    try:
        # 只清理存储数据，不清理网络缓存，避免影响登录状态
        driver.execute_cdp_cmd('Storage.clearDataForOrigin', {
            'origin': '*',
            'storageTypes': 'local_storage,indexeddb,websql,file_systems,shader_cache,service_workers',
            'quotaTypes': 'all'
        })
        print("已清理存储数据（保留cookies和网络缓存）")
        
        # 可选：清理Service Workers
        try:
            driver.execute_cdp_cmd('ServiceWorker.stopAllWorkers', {})
            print("已停止所有Service Workers")
        except Exception:
            pass  # Service Worker清理失败不影响主流程
        
        print("Chrome缓存清理完成")
    except Exception as e:
        print(f"使用Chrome清理功能时出错: {e}")
        # 如果Chrome清理失败，回退到手动清理
        fallback_cleanup_chrome_cache()


def fallback_cleanup_chrome_cache():
    """回退方案：手动清理Chrome缓存文件"""
    temp_dir = os.path.join(os.getcwd(), 'temp')
    if not os.path.exists(temp_dir):
        return
    
    # 只清理最安全的缓存目录
    safe_cache_items = [
        'Cache', 'Code Cache', 'GPUCache', 'Service Worker'
    ]
    
    try:
        for item in safe_cache_items:
            item_path = os.path.join(temp_dir, 'Default', item)
            if os.path.exists(item_path):
                if os.path.isdir(item_path):
                    import shutil
                    shutil.rmtree(item_path)
                    print(f"已手动清理缓存目录: {item}")
        
        # 清理其他配置文件的缓存
        for subdir in ['Default', 'Profile 1', 'Profile 2', 'Profile 3']:
            profile_dir = os.path.join(temp_dir, subdir)
            if os.path.exists(profile_dir):
                for cache_dir in ['Cache', 'Code Cache', 'GPUCache']:
                    cache_path = os.path.join(profile_dir, cache_dir)
                    if os.path.exists(cache_path):
                        import shutil
                        shutil.rmtree(cache_path)
                        print(f"已手动清理配置文件缓存: {subdir}/{cache_dir}")
        
        print("手动Chrome缓存清理完成")
    except Exception as e:
        print(f"手动清理Chrome缓存时出错: {e}")


def prelaunch_guard_preferences(user_data_dir: str, max_bytes: int = 50 * 1024 * 1024):
    """启动前检查各 Profile 的 Preferences 体积，超出阈值则备份并删除。

    参数：
    - user_data_dir: Chrome 用户数据目录（--user-data-dir 对应路径）
    - max_bytes: 阈值，默认 50MB；超过则视为异常并清理
    """
    try:
        if not os.path.isdir(user_data_dir):
            return
        candidates = []
        for name in os.listdir(user_data_dir):
            base = os.path.join(user_data_dir, name)
            if os.path.isdir(base) and (name == 'Default' or name.startswith('Profile')):
                candidates.append(os.path.join(base, 'Preferences'))
        for fp in candidates:
            try:
                if os.path.exists(fp):
                    size = os.path.getsize(fp)
                    if size >= max_bytes:
                        # 优先尝试截断为 0 字节，失败再删除
                        try:
                            with open(fp, 'w') as f:
                                f.truncate(0)
                            print(f"Preferences 超过阈值({size} bytes)，已截断: {fp}")
                        except Exception:
                            try:
                                os.remove(fp)
                                print(f"Preferences 超过阈值({size} bytes)，截断失败已删除: {fp}")
                            except Exception:
                                pass
            except Exception:
                pass
    except Exception as e:
        print(f"启动前检查 Preferences 出错: {e}")


def cleanup_chrome_preferences():
    # 已废弃：根据需求，仅保留启动前检查，不在退出时清理
    return


def get_page(url):
    global driver
    max_retries = 3
    for attempt in range(max_retries):
        try:
            if driver is None:
                init_driver()  # 如果未初始化，则初始化 Firefox 实例
            driver.get(url)
            start_time = time.time()
            while True:
                if time.time() - start_time > 5:
                    print("页面加载超时，正在重新尝试...")
                    break
                if driver.execute_script("return document.readyState") == "complete":
                    break
                time.sleep(1)
            # 新增：检测Security Verification页面并自动点击checkbox
            html = driver.page_source
            if 'Security Verification' in html:
                print('检测到Security Verification页面，等待出现“确认您是真人”再点击...')
                from selenium.webdriver.common.by import By
                from selenium.webdriver.support.ui import WebDriverWait
                from selenium.webdriver.support import expected_conditions as EC
                from selenium.webdriver.common.action_chains import ActionChains
                try:
                    # 基于文字检测进行点击
                    phrase = '确认您是真人'
                    clicked = _try_dom_text_click(phrase, timeout_sec=8)
                    # 若仍未成功，再使用视觉/元素复合方式
                    if not clicked:
                        # 优先尝试基于截图的视觉点击（正方形）
                        clicked = _try_vision_click_checkbox()
                    if not clicked:
                        # 遍历上下文元素点击复选框
                        iframes = driver.find_elements(By.TAG_NAME, "iframe")
                        total_ctx = len(iframes) + 1
                        for i in range(total_ctx):
                            driver.switch_to.default_content()
                            if i < len(iframes):
                                try:
                                    driver.switch_to.frame(iframes[i])
                                except Exception:
                                    continue
                            checkbox = None
                            selectors = [
                                "label.cb-lb input[type='checkbox']",
                                "input[type='checkbox']",
                                "input"
                            ]
                            for sel in selectors:
                                try:
                                    if driver is None:
                                        break
                                    WebDriverWait(driver, 5).until(
                                        EC.presence_of_element_located((By.CSS_SELECTOR, sel if sel != "input" else "input"))
                                    )
                                    checkbox = driver.find_element(By.CSS_SELECTOR, sel if sel != "input" else "input")
                                    break
                                except Exception:
                                    continue
                            if checkbox is None:
                                continue
                            try:
                                if driver is not None:
                                    driver.execute_script("arguments[0].scrollIntoView({block:'center'});", checkbox)
                                    time.sleep(0.2)
                                    ActionChains(driver).move_to_element(checkbox).pause(0.1).click().perform()
                                    clicked = True
                            except Exception:
                                try:
                                    pos = driver.execute_script(
                                        "const r=arguments[0].getBoundingClientRect(); return {x:r.left + r.width/2, y:r.top + r.height/2};",
                                        checkbox
                                    )
                                    if pos and 'x' in pos and 'y' in pos:
                                        jitter_x = random.uniform(-2, 2)
                                        jitter_y = random.uniform(-2, 2)
                                        ok = _cdp_click_viewport(pos['x'] + jitter_x, pos['y'] + jitter_y)
                                        clicked = bool(ok)
                                except Exception:
                                    pass
                            time.sleep(1.5)
                            if clicked:
                                break
                    driver.switch_to.default_content()
                    html = driver.page_source
                    if clicked:
                        print('已完成验证码点击（基于文字/视觉/元素）')
                        # 等待页面内容加载完毕后再返回
                        html = _wait_for_content_loaded(timeout_sec=25)
                        # 验证完成后重载页面，防止任务遗漏
                        if html and 'Security Verification' not in html:
                            print('验证完成，重载页面确保内容完整...')
                            try:
                                driver.refresh()
                                time.sleep(3)  # 等待页面重新加载
                                html = _wait_for_content_loaded(timeout_sec=25)
                                print('页面重载完成')
                            except Exception as reload_error:
                                print(f'页面重载失败: {reload_error}')
                    else:
                        raise Exception('未找到可点击的目标')
                except Exception as e:
                    print(f'未能自动点击checkbox: {e.__class__.__name__}: {e}')
                    driver.save_screenshot("security_verification_error.png")
                    # 保存主页面源代码
                    try:
                        with open("security_verification_error.html", "w", encoding="utf-8") as f:
                            f.write(driver.page_source)
                        print("已保存主页面源代码到 security_verification_error.html")
                    except Exception as err:
                        print(f"保存主页面源代码失败: {err}")
                    # 保存所有iframe内容
                    try:
                        from selenium.webdriver.common.by import By
                        iframes = driver.find_elements(By.TAG_NAME, "iframe")
                        for idx in range(len(iframes)):
                            driver.switch_to.default_content()
                            iframe = driver.find_elements(By.TAG_NAME, "iframe")[idx]
                            driver.switch_to.frame(iframe)
                            with open(f"security_verification_iframe_{idx}.html", "w", encoding="utf-8") as f:
                                f.write(driver.page_source)
                            print(f"已保存iframe {idx} 源代码到 security_verification_iframe_{idx}.html")
                        driver.switch_to.default_content()
                    except Exception as err:
                        print(f"保存iframe源代码失败: {err}")
                    # 保存网络请求内容（HAR-like）
                    try:
                        driver.execute_cdp_cmd('Network.enable', {})
                        import json
                        logs = driver.get_log('performance') if hasattr(driver, 'get_log') else []
                        with open('security_verification_network.json', 'w', encoding='utf-8') as f:
                            json.dump([log['message'] for log in logs], f, ensure_ascii=False, indent=2)
                        print('已保存网络请求内容到 security_verification_network.json')
                    except Exception as err:
                        print(f'保存网络内容失败: {err}')
                    # 保存所有shadow DOM内容
                    try:
                        shadow_script = '''
                        function getAllShadowRoots(node) {
                            let result = [];
                            if (node.shadowRoot) {
                                result.push(node.shadowRoot.innerHTML);
                                Array.from(node.shadowRoot.querySelectorAll('*')).forEach(child => {
                                    result = result.concat(getAllShadowRoots(child));
                                });
                            }
                            Array.from(node.children).forEach(child => {
                                result = result.concat(getAllShadowRoots(child));
                            });
                            return result;
                        }
                        return getAllShadowRoots(document.body);
                        '''
                        shadow_htmls = driver.execute_script(shadow_script)
                        with open('security_verification_shadowdom.html', 'w', encoding='utf-8') as f:
                            for html in shadow_htmls:
                                f.write(html)
                                f.write('\n<!-- SHADOW DOM SPLIT -->\n')
                        print('已保存所有shadow DOM内容到 security_verification_shadowdom.html')
                    except Exception as err:
                        print(f'保存shadow DOM内容失败: {err}')
            # 检查HTTP响应码（通过performance API）
            status_code = None
            try:
                perf_entries = driver.execute_script("return window.performance.getEntries();")
                for entry in perf_entries:
                    if entry.get('initiatorType') == 'navigation' and 'responseStatus' in entry:
                        status_code = entry['responseStatus']
                        break
            except Exception:
                pass
            if status_code == 429:
                print('检测到HTTP 429，等待10秒后重试...')
                time.sleep(10)
                continue
            return BeautifulSoup(html, 'html.parser')
        except Exception as e:
            print(f"获取页面失败: {e}")
            if attempt < max_retries - 1:
                print(f"尝试重新加载页面，第 {attempt + 1} 次...")
                continue
            else:
                print("连续三次尝试加载页面失败，标记为网络不佳无法获取。")
                return None


def get_url(cli_url: Optional[str] = None, default_value: str = "https://javdb.com/tags?c10=1,2", use_gui: bool = True) -> Tuple[Union[str, List[str]], str, bool]:
    if cli_url:
        parsed = urllib.parse.urlparse(cli_url)
        return cli_url, parsed.scheme + '://' + parsed.netloc, False
    if use_gui and tk and simpledialog:
        root = tk.Tk()  # 使用 tk.Tk() 创建窗口
        root.withdraw()
        # 询问是否启用 list 模式
        enable_list_mode = messagebox.askyesno("模式选择", "是否启用 list 模式（批量处理）？")
        if enable_list_mode:
            try:
                with open("list.txt", "r", encoding="utf-8") as f:
                    urls = [line.strip() for line in f if line.strip()]
                if not urls:
                    messagebox.showerror("错误", "list.txt 文件为空或格式不正确！")
                    parsed = urllib.parse.urlparse(default_value)
                    return default_value, parsed.scheme + '://' + parsed.netloc, False
                # 在每个链接后添加参数 ?t=c&sort_type=0
                processed_urls = []
                for url in urls:
                    if '?' not in url:
                        processed_urls.append(f"{url}?t=c&sort_type=0")
                    else:
                        processed_urls.append(f"{url}&t=c&sort_type=0")
                parsed = urllib.parse.urlparse(processed_urls[0])
                return processed_urls, parsed.scheme + '://' + parsed.netloc, True
            except FileNotFoundError:
                messagebox.showerror("错误", "未找到 list.txt 文件！")
                parsed = urllib.parse.urlparse(default_value)
                return default_value, parsed.scheme + '://' + parsed.netloc, False
        else:
            url = simpledialog.askstring("输入框", "请输入一个字符串", initialvalue=default_value)
            if not url:
                url = default_value
    else:
        url = default_value
    parsed = urllib.parse.urlparse(url)
    return url, parsed.scheme + '://' + parsed.netloc, False


def get_video_titles(soup):
    video_title_tags = soup.find_all('div', class_='video-title')
    titles = []
    for tag in video_title_tags:
        strong_tag = tag.find('strong')
        if strong_tag:
            title = strong_tag.get_text()
            href = strong_tag.parent.parent['href']
            titles.append((title, href))
    return titles


def get_next_page_url(soup, url):
    next_page_tag = soup.find('a', rel='next', class_='pagination-next')
    if next_page_tag:
        next_page_url = urllib.parse.urljoin(url, next_page_tag['href'])
        return next_page_url
    else:
        return None


def _save_cover_from_dom(save_path: str) -> bool:
    global driver
    try:
        from selenium.webdriver.common.by import By
        imgs = driver.find_elements(By.TAG_NAME, "img")
        candidates = []
        for img in imgs:
            try:
                src = img.get_attribute("src") or ""
                low = src.lower()
                if ("/covers/" in low or "jdbstatic" in low) and low.endswith(".jpg"):
                    candidates.append(img)
            except Exception:
                continue
        if not candidates:
            for img in imgs:
                try:
                    src = img.get_attribute("src") or ""
                    if src.lower().endswith(".jpg"):
                        candidates.append(img)
                except Exception:
                    continue
        for el in candidates:
            try:
                driver.execute_script("arguments[0].scrollIntoView({block:'center'});", el)
                time.sleep(0.2)
                el.screenshot(save_path)
                return True
            except Exception:
                continue
    except Exception as e:
        print(f"从DOM保存封面失败: {e}")
    return False


def _screenshot_image_to_file(image_url: str, save_path: str):
    global driver
    try:
        current = driver.current_window_handle
        driver.switch_to.new_window('tab')
        # 设置窗口大小以便截图更清晰
        try:
            driver.set_window_size(600, 900)
        except Exception:
            pass
        driver.get(image_url)
        time.sleep(1)
        png = driver.get_screenshot_as_png()
        with open(save_path, 'wb') as f:
            f.write(png)
    except Exception as e:
        print(f"封面截图失败: {e}")
    finally:
        try:
            driver.close()
            driver.switch_to.window(current)
        except Exception:
            pass


def _extract_cover_src_from_soup(soup: BeautifulSoup, base_url: str) -> Optional[str]:
    # 优先找包含 covers 路径的 jpg 图片
    try:
        for img in soup.find_all('img'):
            src = img.get('src') or img.get('data-src') or ''
            if not src:
                continue
            low = src.lower()
            if ('covers/' in low or 'jdbstatic' in low) and low.endswith('.jpg'):
                return urllib.parse.urljoin(base_url, src)
        # 退化：返回第一个 jpg
        for img in soup.find_all('img'):
            src = img.get('src') or img.get('data-src') or ''
            if src.lower().endswith('.jpg'):
                return urllib.parse.urljoin(base_url, src)
    except Exception:
        pass
    return None


def get_magnet_link(title_url: str, magnet_filter: Optional[str] = None, save_cover_dir: Optional[str] = None) -> Tuple[List[str], Optional[str]]:
    max_retries = 3
    for i in range(max_retries):
        try:
            soup = get_page(title_url)
            if soup is None:
                print("无法获取页面内容")  # 修改：日志记录
                return [], None
            magnet_tags = soup.find_all('a', href=re.compile(r'magnet:.*'))
            # 先选择符合条件的第一个磁链
            selected_link = None
            if magnet_filter:
                magnet_filter_l = magnet_filter.lower()
                for i, magnet_tag in enumerate(magnet_tags):
                    link = magnet_tag.get('href', '')
                    text = magnet_tag.get_text(strip=True) or ""
                    # 更严格的匹配：确保过滤条件完整匹配
                    link_lower = link.lower()
                    text_lower = text.lower()
                    # 检查是否包含过滤条件，但排除包含其他选项的情况
                    if magnet_filter_l in link_lower or magnet_filter_l in text_lower:
                        
                        # 更严格的检查：确保只包含目标过滤条件，不包含其他选项
                        combined_text = link_lower + " " + text_lower
                        
                        # 如果是 -UC，确保不包含 -C（只检查包含"-"的完整关键字）
                        if magnet_filter_l == "-uc":
                            if "-c" in combined_text:
                                continue
                            # 额外检查：确保文本中确实包含 -UC
                            if "-uc" not in combined_text:
                                continue
                        
                        # 如果是 -C，确保不包含 -UC（只检查包含"-"的完整关键字）
                        if magnet_filter_l == "-c":
                            if "-uc" in combined_text:
                                continue
                            # 额外检查：确保文本中确实包含 -C
                            if "-c" not in combined_text:
                                continue
                        
                        selected_link = link
                        break
            else:
                selected_link = magnet_tags[0]['href'] if magnet_tags else None
            # 仅在有磁链时才保存封面
            cover_local_path = None
            if selected_link and save_cover_dir:
                try:
                    parsed = urllib.parse.urlparse(title_url)
                    vid = parsed.path.strip('/').split('/')[-1]
                    if vid:
                        os.makedirs(save_cover_dir, exist_ok=True)
                        cover_local_path = os.path.join(save_cover_dir, f"{vid}.png")
                        # 优先直接截取当前页面中的封面元素
                        ok = _save_cover_from_dom(cover_local_path)
                        if not ok:
                            # 回退：解析页面中的封面URL，打开新标签截图
                            base_url = f"{parsed.scheme}://{parsed.netloc}"
                            cover_src = _extract_cover_src_from_soup(soup, base_url)
                            if cover_src:
                                _screenshot_image_to_file(cover_src, cover_local_path)
                            else:
                                cover_local_path = None
                except Exception as e:
                    print(f"封面保存失败: {e}")
            return ([selected_link] if selected_link else []), cover_local_path
        except Exception as e:
            print(f"获取页面失败，第 {i+1} 次重试... 错误信息: {e}")
            if i < max_retries - 1:  # i是从0开始的
                time.sleep(3)  # 稍微等待一下后重试
                continue
            else:
                print("获取页面失败，请检查网络或网站是否可访问。")
                return [], None  # 返回空列表和None而不是抛出异常

    # 理论上不会到达这里，但为了类型检查器添加
    return [], None


def check_title_existence(title):
    keyword, href = title
    result = get_search_result(keyword)[2]
    print(f'检查标题 "{keyword}": {"存在" if result else "不存在"}')  # 修改：日志记录
    return (keyword, href) if not result else (keyword, None)


def _cdp_click_viewport(x: int, y: int):
    try:
        driver.execute_cdp_cmd('Input.dispatchMouseEvent', {"type": "mouseMoved", "x": int(x), "y": int(y)})
        driver.execute_cdp_cmd('Input.dispatchMouseEvent', {"type": "mousePressed", "x": int(x), "y": int(y), "button": "left", "clickCount": 1})
        driver.execute_cdp_cmd('Input.dispatchMouseEvent', {"type": "mouseReleased", "x": int(x), "y": int(y), "button": "left", "clickCount": 1})
        return True
    except Exception as e:
        print(f"CDP 点击失败: {e}")
        return False


def _try_dom_text_click(phrase: str, timeout_sec: float = 0) -> bool:
    from selenium.webdriver.common.by import By
    end = time.time() + max(timeout_sec, 0)
    while True:
        try:
            driver.switch_to.default_content()
            iframes = driver.find_elements(By.TAG_NAME, "iframe")
            # 预先获取每个 iframe 的视口偏移
            iframe_rects = []
            for idx in range(len(iframes)):
                try:
                    rect = driver.execute_script(
                        "const r=arguments[0].getBoundingClientRect(); return {x:r.left, y:r.top};",
                        iframes[idx]
                    )
                    iframe_rects.append(rect or {"x": 0, "y": 0})
                except Exception:
                    iframe_rects.append({"x": 0, "y": 0})
            total_ctx = len(iframes) + 1
            for i in range(total_ctx):
                driver.switch_to.default_content()
                offset_x = 0
                offset_y = 0
                if i < len(iframes):
                    try:
                        driver.switch_to.frame(iframes[i])
                        off = iframe_rects[i]
                        offset_x = off.get("x", 0)
                        offset_y = off.get("y", 0)
                    except Exception:
                        continue
                # 在当前上下文搜索包含目标短语的元素
                el = None
                selectors = [
                    "//*[contains(normalize-space(.), '" + phrase + "')]",
                    "//*[text()[contains(., '" + phrase + "')]]"
                ]
                for xp in selectors:
                    try:
                        el = driver.find_element(By.XPATH, xp)
                        break
                    except Exception:
                        continue
                if el is None:
                    continue
                # 取文字区域中心进行 CDP 点击
                try:
                    rect = driver.execute_script(
                        "const r=arguments[0].getBoundingClientRect(); return {x:r.left + r.width/2, y:r.top + r.height/2};",
                        el
                    )
                    if rect and 'x' in rect and 'y' in rect:
                        jitter_x = random.uniform(-2, 2)
                        jitter_y = random.uniform(-2, 2)
                        ok = _cdp_click_viewport(rect['x'] + offset_x + jitter_x, rect['y'] + offset_y + jitter_y)
                        if ok:
                            time.sleep(1.5)
                            return True
                except Exception as e:
                    print(f"DOM 文字点击失败: {e}")
                    continue
        except Exception as e:
            print(f"DOM 文字查找失败: {e}")
        if time.time() > end:
            return False
        time.sleep(0.5)


# OCR功能已移除


def _wait_for_content_loaded(timeout_sec: float = 20):
    from selenium.webdriver.common.by import By
    end = time.time() + max(timeout_sec, 0)
    last_html = None
    while True:
        try:
            state = driver.execute_script("return document.readyState")
        except Exception:
            state = None
        try:
            html = driver.page_source
            last_html = html
        except Exception:
            html = ""
        # 判定条件：ready 完成 且 不再显示 Security Verification 且 有主要内容元素
        has_titles = False
        try:
            if driver.find_elements(By.CSS_SELECTOR, 'div.video-title'):
                has_titles = True
        except Exception:
            pass
        if state == 'complete' and ('Security Verification' not in (html or '')) and has_titles:
            return html
        if time.time() > end:
            return last_html or html
        time.sleep(0.5)


def _vision_find_square_center(png_bytes: bytes):
    if np is None or cv2 is None:
        return None
    try:
        buf = np.frombuffer(png_bytes, dtype=np.uint8)
        img = cv2.imdecode(buf, cv2.IMREAD_COLOR)
        if img is None:
            return None
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        gray = cv2.GaussianBlur(gray, (5, 5), 0)
        edges = cv2.Canny(gray, 50, 150)
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        best = None
        best_area = 0
        h_img, w_img = gray.shape[:2]
        for cnt in contours:
            peri = cv2.arcLength(cnt, True)
            if peri < 20:
                continue
            approx = cv2.approxPolyDP(cnt, 0.04 * peri, True)
            if len(approx) == 4 and cv2.isContourConvex(approx):
                x, y, w, h = cv2.boundingRect(approx)
                area = w * h
                if area < 500 or area > (w_img * h_img * 0.25):
                    continue
                ratio = w / float(h) if h else 0
                if 0.75 <= ratio <= 1.25:
                    if area > best_area:
                        best_area = area
                        best = (x + w // 2, y + h // 2)
        return best
    except Exception as e:
        print(f"视觉识别失败: {e}")
        return None


def _try_vision_click_checkbox() -> bool:
    try:
        # 截取可视区域截图
        png = driver.get_screenshot_as_png()
        center = _vision_find_square_center(png)
        if center is None:
            return False
        x, y = center
        ok = _cdp_click_viewport(x, y)
        if ok:
            time.sleep(1.5)
        return ok
    except Exception as e:
        print(f"视觉点击流程失败: {e}")
        return False


def main():
    global driver
    parser = argparse.ArgumentParser(description="JAVDB 抓取工具（支持无头/CLI 模式）")
    parser.add_argument("--url", help="起始 URL，如 https://javdb.com/tags?c10=1,2")
    parser.add_argument("--headless", action="store_true", help="使用无头浏览器运行")
    parser.add_argument("--no-gui", action="store_true", help="禁用 GUI 弹窗，完全命令行模式")
    parser.add_argument("--magnets", action="store_true", help="是否抓取磁链")
    parser.add_argument("--max-pages", type=int, default=0, help="最大翻页数，0 表示不限")
    parser.add_argument("--filters", nargs='*', help="按标题前缀筛选（空格分隔多项）")
    parser.add_argument("--magnet-filter", type=str, default=None, help="磁链过滤子串（可匹配链接或文本）")
    parser.add_argument("--magnet-choice", choices=["-UC", "-C"], nargs='*', help="磁链过滤关键字，可多选：-UC、-C；多选时按 -UC 优先再 -C")
    parser.add_argument("--limit", type=int, default=0, help="抓取磁链的条目上限，0 表示不限")
    parser.add_argument("--no-clipboard", action="store_true", help="不复制结果到剪贴板")
    args = parser.parse_args()

    import sys
    use_gui: bool = (not args.no_gui) and (tk is not None) and (bool(os.environ.get("DISPLAY")) or sys.platform.startswith("win") or sys.platform == "darwin")

    try:
        init_driver(headless=args.headless)  # 初始化 Chrome 实例
        url_result, schemdomain, is_list_mode = get_url(cli_url=args.url, use_gui=use_gui)

        # 处理list模式和单URL模式
        if is_list_mode:
            # list模式：批量处理多个URL
            urls = url_result
            all_titles = []

            for i, current_url in enumerate(urls):
                print(f"正在处理第 {i+1}/{len(urls)} 个链接: {current_url}")
                soup = get_page(current_url)
                if soup is None:
                    print(f"无法获取页面内容: {current_url}")
                    continue

                titles = get_video_titles(soup)
                if len(titles) == 0:
                    print(f"没有找到视频标题: {current_url}")
                    continue

                # 在list模式下，处理所有页面，不询问用户
                next_page_url = get_next_page_url(soup, current_url)
                page_count = 1
                while next_page_url:
                    if args.max_pages and page_count >= args.max_pages:
                        break
                    soup = get_page(next_page_url)
                    if soup is None:
                        print(f"无法获取下一页内容: {next_page_url}")
                        break
                    titles.extend(get_video_titles(soup))
                    next_page_url = get_next_page_url(soup, next_page_url)
                    page_count += 1

                all_titles.extend(titles)
                print(f"链接 {i+1} 处理完成，获取到 {len(titles)} 个标题")

            titles = all_titles
            print(f"批量处理完成，总共获取到 {len(titles)} 个标题")
        else:
            # 单URL模式：原有逻辑
            url = url_result
            soup = get_page(url)
            if soup is None:
                if use_gui and messagebox:
                    messagebox.showerror("错误", "无法获取页面内容")  # 保留：用户操作弹窗
                else:
                    print("无法获取页面内容")
                return
            titles = get_video_titles(soup)
            if len(titles) == 0:
                print("没有找到视频标题")  # 修改：日志记录
                with open('javdb.html', 'w', encoding='utf-8') as f:
                    f.write(soup.prettify())

            next_page_url = get_next_page_url(soup, url)
            page_count = 1  # 新增：用于计数已经获取的页面数量
            while next_page_url:
                if args.max_pages and page_count >= args.max_pages:
                    break
                soup = get_page(next_page_url)
                if soup is None:
                    print("无法获取下一页内容")  # 修改：日志记录
                    break
                titles.extend(get_video_titles(soup))
                next_page_url = get_next_page_url(soup, next_page_url)

                page_count += 1
                # 只在非list模式下询问用户
                if use_gui and (page_count % 5 == 0) and messagebox and not is_list_mode:  # 每5页提示一次
                    response = messagebox.askyesno("提示", "已经获取了5页的数据，是否继续？")  # 保留：用户操作弹窗
                    if not response:
                        break

        # 新增：筛选功能
        # 1. 提取所有标题的第一部分
        first_parts = [title[0].split('-')[0].strip() for title in titles]
        unique_first_parts = sorted(set(first_parts))

        # 2. 读取上次选择
        import json
        memory_file = os.path.join(os.getcwd(), 'javdbid_select_memory.json')

        if os.path.exists(memory_file):
            try:
                with open(memory_file, 'r', encoding='utf-8') as f:
                    last_selected = json.load(f)
            except Exception:
                last_selected = []
        else:
            last_selected = []

        selected_parts = None

        if args.filters:
            selected_parts = args.filters
        elif use_gui:
            # 仅在 GUI 可用时定义并调用多选窗口
            def ask_multi_choice_move(options, title="请选择需要获取的部分", preselect=None):
                root = tk.Tk()
                root.withdraw()
                win = tk.Toplevel(root)
                win.title(title)
                win.geometry("600x400")
                left_lb = tk.Listbox(win, selectmode=tk.MULTIPLE, exportselection=False)
                left_lb.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
                right_lb = tk.Listbox(win, selectmode=tk.MULTIPLE, exportselection=False)
                right_lb.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=10, pady=10)
                # 填充左右侧
                preselect = set(preselect) if preselect else set()
                for opt in options:
                    if opt in preselect:
                        right_lb.insert(tk.END, opt)
                    else:
                        left_lb.insert(tk.END, opt)
                btn_frame = tk.Frame(win)
                btn_frame.pack(side=tk.LEFT, fill=tk.Y)
                def move_right():
                    selected = list(left_lb.curselection())[::-1]
                    for idx in selected:
                        val = left_lb.get(idx)
                        right_lb.insert(tk.END, val)
                        left_lb.delete(idx)
                def move_left():
                    selected = list(right_lb.curselection())[::-1]
                    for idx in selected:
                        val = right_lb.get(idx)
                        left_lb.insert(tk.END, val)
                        right_lb.delete(idx)
                def select_all():
                    # 全部移到右侧
                    for idx in range(left_lb.size()-1, -1, -1):
                        val = left_lb.get(idx)
                        right_lb.insert(tk.END, val)
                        left_lb.delete(idx)
                def deselect_all():
                    # 全部移到左侧
                    for idx in range(right_lb.size()-1, -1, -1):
                        val = right_lb.get(idx)
                        left_lb.insert(tk.END, val)
                        right_lb.delete(idx)
                btn1 = tk.Button(btn_frame, text=">>", command=move_right)
                btn1.pack(pady=10)
                btn2 = tk.Button(btn_frame, text="<<", command=move_left)
                btn2.pack(pady=10)
                btn3 = tk.Button(btn_frame, text="全选", command=select_all)
                btn3.pack(pady=10)
                btn4 = tk.Button(btn_frame, text="全取消", command=deselect_all)
                btn4.pack(pady=10)
                result = []
                def on_ok():
                    result.extend([right_lb.get(i) for i in range(right_lb.size())])
                    win.destroy()
                    root.quit()
                btn_ok = tk.Button(win, text="确定", command=on_ok)
                btn_ok.pack(side=tk.BOTTOM, pady=10)
                root.mainloop()
                return result

            selected_parts = ask_multi_choice_move(unique_first_parts, preselect=last_selected)
        else:
            # 无 GUI 模式：优先使用上次选择，否则全选
            selected_parts = last_selected if last_selected else unique_first_parts

        # 保存本次选择
        try:
            with open(memory_file, 'w', encoding='utf-8') as f:
                json.dump(selected_parts, f, ensure_ascii=False)
        except Exception as e:
            print(f"保存选择记录失败: {e}")
        if not selected_parts:
            if use_gui and messagebox:
                messagebox.showinfo("提示", "未选择任何部分，程序结束。")
            else:
                print("未选择任何部分，程序结束。")
            return

        # 3. 只保留标题第一部分在勾选列表中的条目
        filtered_titles = [t for t in titles if t[0].split('-')[0].strip() in selected_parts]
        titles_str = ' '.join([title[0] for title in filtered_titles])
        print(f"获取到的视频标题: {titles_str}")

        # 是否获取磁链
        fetch_magnets = args.magnets
        if use_gui and not args.magnets and messagebox:
            fetch_magnets = messagebox.askyesno("提示", "是否获取magnet链接？")

        # 选择磁链过滤（支持 UC/C 多选，UC 优先）
        selected_choices = list(args.magnet_choice or [])
        if fetch_magnets and not selected_choices and not args.magnet_filter:
            if use_gui and tk:
                # 构建复选对话框，允许同时选择 UC 与 C
                def ask_filter_multi():
                    root = tk.Tk()
                    root.withdraw()
                    win = tk.Toplevel(root)
                    win.title("选择过滤关键字（可多选）")
                    win.geometry("300x150")
                    
                    # 使用全局变量来存储选择状态
                    selected_uc = False
                    selected_c = False
                    
                    def on_uc_click():
                        nonlocal selected_uc
                        selected_uc = not selected_uc
                        print(f"UC复选框点击，状态: {selected_uc}")
                    
                    def on_c_click():
                        nonlocal selected_c
                        selected_c = not selected_c
                        print(f"C复选框点击，状态: {selected_c}")
                    
                    # 创建复选框并绑定点击事件
                    cb_uc = tk.Checkbutton(win, text="-UC", command=on_uc_click)
                    cb_uc.pack(anchor="w", padx=12, pady=6)
                    cb_c = tk.Checkbutton(win, text="-C", command=on_c_click)
                    cb_c.pack(anchor="w", padx=12, pady=6)
                    
                    result = []
                    def on_ok():
                        print(f"GUI调试 - selected_uc: {selected_uc}")
                        print(f"GUI调试 - selected_c: {selected_c}")
                        if selected_uc:
                            result.append("-UC")
                        if selected_c:
                            result.append("-C")
                        print(f"GUI调试 - 选择结果: {result}")
                        # 如果用户没有选择任何选项，默认选择-UC
                        if not result:
                            result.append("-UC")
                            print(f"GUI调试 - 添加默认值后: {result}")
                        win.destroy()
                        root.quit()
                    
                    tk.Button(win, text="确定", command=on_ok).pack(pady=10)
                    root.mainloop()
                    return result
                selected_choices = ask_filter_multi()
            else:
                # 无 GUI 时，如果没有指定选择，则不设置默认值，让用户明确选择
                selected_choices = []
        # 规范化顺序：UC 优先
        if selected_choices:
            print(f"规范化前的选择: {selected_choices}")
            selected_choices = [c for c in ["-UC", "-C"] if c in set(selected_choices)]
            print(f"规范化后的选择: {selected_choices}")
        # 自由过滤串（若提供则忽略 choices）
        magnet_filter_value = args.magnet_filter if args.magnet_filter else None

        # 记录选择的过滤关键词
        if fetch_magnets:
            if magnet_filter_value:
                print(f"使用自由过滤关键词: {magnet_filter_value}")
            elif selected_choices:
                if len(selected_choices) == 1:
                    print(f"选择单一过滤关键词: {selected_choices[0]}")
                else:
                    print(f"选择多个过滤关键词: {selected_choices} (优先使用-UC，无结果时使用-C)")
            else:
                print("使用默认过滤策略: 优先-UC，无结果时使用-C")

        if fetch_magnets:
            with ThreadPoolExecutor(max_workers=10) as executor:
                results = list(executor.map(check_title_existence, filtered_titles))
            titles = [result for result in results if result[1] is not None]
            titles2 = [result for result in results if result[1] is None]
            for (keyword, _) in titles2:
                print(f'已经存在：{keyword}')
            magnet_links = []
            no_magnet_links = []
            interrupted = False
            # 新增：为每个标题记录封面与磁链
            title_to_info = {}
            # 封面缓存目录
            pictemp_dir = os.path.join(os.getcwd(), 'pictemp')
            # 新增：磁链-title-图片名对照表
            mapping = []
            try:
                count = 0
                for idx, title in enumerate(titles):
                    if args.limit and count >= args.limit:
                        break
                    title_text, title_href = title
                    title_url = title_href
                    while True:
                        try:
                            magnets = []
                            cover_local = None
                            if magnet_filter_value:
                                # 使用自由过滤串，严格匹配
                                magnets, cover_local = get_magnet_link(schemdomain + title_url, magnet_filter=magnet_filter_value, save_cover_dir=pictemp_dir)
                            else:
                                # 根据选择的选项数量决定过滤策略
                                if len(selected_choices) == 1:
                                    # 单选一项时，仅使用这一个关键词过滤
                                    ch = selected_choices[0]
                                    magnets, cover_local = get_magnet_link(schemdomain + title_url, magnet_filter=ch, save_cover_dir=pictemp_dir)
                                elif len(selected_choices) >= 2:
                                    # 都选择时，优先使用-UC，若-UC过滤后没有结果的情况下再使用-C过滤
                                    magnets, cover_local = get_magnet_link(schemdomain + title_url, magnet_filter="-UC", save_cover_dir=pictemp_dir)
                                    if not magnets:
                                        magnets, cover_local = get_magnet_link(schemdomain + title_url, magnet_filter="-C", save_cover_dir=pictemp_dir)
                                else:
                                    # 没有选择时，使用默认优先级
                                    magnets, cover_local = get_magnet_link(schemdomain + title_url, magnet_filter="-UC", save_cover_dir=pictemp_dir)
                                    if not magnets:
                                        magnets, cover_local = get_magnet_link(schemdomain + title_url, magnet_filter="-C", save_cover_dir=pictemp_dir)
                            break
                        except Exception:
                            print('获取页面失败，正在重试...')
                            time.sleep(3)
                    # 记录磁链与封面信息（仅在有磁链时）
                    if magnets:
                        print(f'{title_text} {magnets}')
                        magnet_links.extend(magnets)
                        title_to_info.setdefault(title_text, {"cover_path": cover_local, "magnets": []})
                        title_to_info[title_text]["magnets"].extend(magnets)
                        image_name = os.path.basename(cover_local) if cover_local else ""
                        for m in magnets:
                            mapping.append({"title": title_text, "magnet": m, "image": image_name})
                    else:
                        print(f'{title_text} 没有magnet链接')
                        no_magnet_links.append(title_text)
                    count += 1
            except KeyboardInterrupt:
                interrupted = True
                print('检测到中断，提前结束。')
            # 保存对照表
            try:
                if mapping and os.path.isdir(pictemp_dir):
                    import json as _json
                    with open(os.path.join(pictemp_dir, 'mapping.json'), 'w', encoding='utf-8') as f:
                        _json.dump(mapping, f, ensure_ascii=False, indent=2)
            except Exception as e:
                print(f"保存mapping失败: {e}")
            # 复制或提示前，增加复核界面（GUI 可用且有磁链且未中断也可复核）
            selected_magnets = list(magnet_links)
            if selected_magnets:
                try:
                    from PyQt5 import QtWidgets, QtGui, QtCore
                    import sys
                    class CoverSelectDialog(QtWidgets.QDialog):
                        def __init__(self, title_to_info, parent=None):
                            super().__init__(parent)
                            self.setWindowTitle("请选择要保留的条目（默认全选）")
                            self.resize(900, 600)
                            self.selected_titles = []
                            self.title_to_info = title_to_info
                            self.max_title_length = 25
                            self.scroll = QtWidgets.QScrollArea(self)
                            self.scroll.setWidgetResizable(True)
                            try:
                                self.scroll.setHorizontalScrollBarPolicy(getattr(QtCore.Qt, 'ScrollBarAlwaysOff', 1))  # type: ignore
                            except (AttributeError, TypeError):
                                pass
                            self.main_widget = QtWidgets.QWidget()
                            self.scroll.setWidget(self.main_widget)
                            self.grid_layout = QtWidgets.QGridLayout(self.main_widget)
                            self.checkboxes = {}
                            self.img_labels = {}
                            self.cells = []
                            self._build_grid()
                            vbox = QtWidgets.QVBoxLayout(self)
                            vbox.addWidget(self.scroll)
                            btn_box = QtWidgets.QHBoxLayout()
                            ok_btn = QtWidgets.QPushButton("确定")
                            cancel_btn = QtWidgets.QPushButton("取消")
                            select_all_btn = QtWidgets.QPushButton("全选")
                            deselect_all_btn = QtWidgets.QPushButton("全取消")
                            btn_box.addStretch(1)
                            btn_box.addWidget(select_all_btn)
                            btn_box.addWidget(deselect_all_btn)
                            btn_box.addWidget(ok_btn)
                            btn_box.addWidget(cancel_btn)
                            vbox.addLayout(btn_box)
                            ok_btn.clicked.connect(self.accept)
                            cancel_btn.clicked.connect(self.reject)
                            select_all_btn.clicked.connect(self.select_all)
                            deselect_all_btn.clicked.connect(self.deselect_all)
                            self.resizeEvent = self._on_resize

                        def select_all(self):
                            for cb in self.checkboxes.values():
                                cb.setChecked(True)

                        def deselect_all(self):
                            for cb in self.checkboxes.values():
                                cb.setChecked(False)

                        def _build_grid(self):
                            # 动态计算列数，保证图片完整显示
                            win_width = self.width()
                            min_tile_w = 240
                            min_gap = 20
                            col_count = max(1, (win_width - min_gap) // (min_tile_w + min_gap))
                            # 进一步保证每列宽度足够显示完整图片
                            while col_count > 1:
                                col_w = (win_width - min_gap * (col_count + 1)) // col_count
                                if col_w < min_tile_w or col_w < 220 + 32:  # 220图片+32边距
                                    col_count -= 1
                                else:
                                    break
                            # 清空原有布局
                            for cell in self.cells:
                                cell.setParent(None)
                            self.cells.clear()
                            self.checkboxes.clear()
                            self.img_labels.clear()
                            row = col = 0
                            for title_text, info in self.title_to_info.items():
                                cell = QtWidgets.QWidget()
                                cell_layout = QtWidgets.QVBoxLayout(cell)
                                top_row = QtWidgets.QHBoxLayout()
                                cb = QtWidgets.QCheckBox()
                                cb.setChecked(True)
                                self.checkboxes[title_text] = cb
                                display_title = title_text[:self.max_title_length] + "..." if len(title_text) > self.max_title_length else title_text
                                title_label = QtWidgets.QLabel(display_title)
                                title_label.setToolTip(title_text)
                                top_row.addWidget(cb)
                                top_row.addWidget(title_label)
                                top_row.addStretch(1)
                                cell_layout.addLayout(top_row)
                                img_label = QtWidgets.QLabel()
                                try:
                                    align_left = getattr(QtCore.Qt, 'AlignLeft', 1)
                                    align_top = getattr(QtCore.Qt, 'AlignTop', 32)
                                    img_label.setAlignment(align_left | align_top)  # type: ignore
                                except (AttributeError, TypeError):
                                    # 兼容不同版本的PyQt5
                                    pass
                                cover_path = info.get("cover_path")
                                if cover_path and os.path.exists(cover_path):
                                    pixmap = QtGui.QPixmap(cover_path)
                                    if not pixmap.isNull():
                                        # 保证图片完整显示
                                        col_w = (win_width - min_gap * (col_count + 1)) // col_count
                                        img_w = min(col_w - 32, pixmap.width())
                                        try:
                                            keep_aspect = getattr(QtCore.Qt, 'KeepAspectRatio', 1)
                                            smooth_transform = getattr(QtCore.Qt, 'SmoothTransformation', 1)
                                            pixmap = pixmap.scaled(img_w, 220, keep_aspect, smooth_transform)  # type: ignore
                                        except (AttributeError, TypeError):
                                            pixmap = pixmap.scaled(img_w, 220)
                                        img_label.setPixmap(pixmap)
                                else:
                                    img_label.setText("封面不可用")
                                self.img_labels[title_text] = img_label
                                cell_layout.addWidget(img_label)
                                self.grid_layout.addWidget(cell, row, col)
                                self.cells.append(cell)
                                # 点击图片和标题等同于点击复选框
                                def toggle_cb(ev, cb=cb):
                                    _ = ev  # 忽略事件参数
                                    cb.setChecked(not cb.isChecked())
                                img_label.mousePressEvent = toggle_cb
                                title_label.mousePressEvent = toggle_cb
                                col += 1
                                if col >= col_count:
                                    col = 0
                                    row += 1

                        def _on_resize(self, a0):
                            self._build_grid()
                            if a0:
                                a0.accept()

                        def get_selected(self):
                            return [t for t, cb in self.checkboxes.items() if cb.isChecked()]

                    _ = QtWidgets.QApplication.instance() or QtWidgets.QApplication(sys.argv)
                    dlg = CoverSelectDialog(title_to_info)
                    if dlg.exec_() == QtWidgets.QDialog.Accepted:
                        selected_titles = dlg.get_selected()
                        selected_magnets = []
                        for t in selected_titles:
                            selected_magnets.extend(title_to_info.get(t, {}).get("magnets", []))
                except Exception as e:
                    print(f"PyQt5复核窗口出错：{e}")
            # 复制或提示
            if selected_magnets:
                try:
                    if not args.no_clipboard:
                        pyperclip.copy('\n'.join(selected_magnets))
                        print('磁链已复制到剪贴板。')
                except Exception as e:
                    print(f"复制到剪贴板失败：{e}")
                # 仅在中断时使用提示窗显示（保持之前约定）
                if interrupted and use_gui and messagebox:
                    messagebox.showinfo("已获取到的磁链", "\n".join(selected_magnets))
            if no_magnet_links:
                print("没有找到magnet链接的标题:")
                print(' '.join(no_magnet_links))
            # 清理封面缓存
            try:
                if os.path.isdir(pictemp_dir):
                    for name in os.listdir(pictemp_dir):
                        fp = os.path.join(pictemp_dir, name)
                        try:
                            if os.path.isfile(fp):
                                os.remove(fp)
                        except Exception:
                            pass
            except Exception:
                pass
        else:
            try:
                if not args.no_clipboard:
                    pyperclip.copy(titles_str)
                    print('标题已复制到剪贴板。')
            except Exception as e:
                print(f"复制到剪贴板失败：{e}")
    finally:
        close_driver()  # 确保在程序结束时关闭 Chrome 实例


if __name__ == "__main__":
    main()
























