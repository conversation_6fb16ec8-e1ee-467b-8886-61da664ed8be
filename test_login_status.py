#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试登录状态的脚本
"""

from get_actors import init_driver_with_temp_profile_copy, kill_chrome_processes, check_chrome_processes
from javdbid import get_page, close_driver
import time


def test_login_status():
    """测试登录状态"""
    
    try:
        # 检查并结束现有Chrome进程
        if check_chrome_processes():
            print("检测到Chrome进程，正在结束...")
            kill_chrome_processes()
        
        # 初始化浏览器
        print("正在初始化浏览器...")
        init_driver_with_temp_profile_copy(headless=False, profile_name="Profile 5")
        
        # 测试访问需要登录的页面
        test_urls = [
            "https://javdb.com/users/collection_actors",
            "https://javdb.com/actors",
            "https://javdb.com"
        ]
        
        for url in test_urls:
            print(f"\n正在测试访问: {url}")
            soup = get_page(url)
            
            if soup:
                title = soup.find('title')
                title_text = title.get_text() if title else "无标题"
                print(f"页面标题: {title_text}")
                
                # 检查是否需要登录
                page_text = soup.get_text().lower()
                if any(indicator in page_text for indicator in ['登入', 'login', '此內容需要登入']):
                    print("❌ 页面需要登录")
                else:
                    print("✅ 页面可以正常访问")
                    
                    # 保存页面用于检查
                    filename = f"test_{url.split('/')[-1] or 'home'}.html"
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(soup.prettify())
                    print(f"页面已保存到: {filename}")
            else:
                print("❌ 无法获取页面内容")
            
            time.sleep(2)
    
    except Exception as e:
        print(f"测试过程中出错: {e}")
    
    finally:
        print("\n正在关闭浏览器...")
        close_driver()


if __name__ == "__main__":
    test_login_status()
