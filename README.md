# Project Usage

## Prerequisites
- Python 3.8+
- Google Chrome/Chromium installed and accessible

Install dependencies:

```bash
pip install -r requirements.txt
```

## javdbid.py

Run headless and fetch titles only:

```bash
python javdbid.py --headless --no-gui --no-clipboard
```

Start from a specific URL and get magnet links with a UC/C choice (returns first match only):

```bash
python javdbid.py --url "https://javdb.com/tags?c10=1,2" \
  --headless --no-gui --magnets --magnet-choice UC --no-clipboard
# or
python javdbid.py --magnets --magnet-choice C
```

You can still use a free-form filter:

```bash
python javdbid.py --magnets --magnet-filter "-UC"
```

Limit pages and items:

```bash
python javdbid.py --max-pages 3 --limit 20
```

## searchlocal.py

Provide keywords in CLI mode:

```bash
python searchlocal.py --keywords ABC-123 DEF-456 --no-clipboard
```

If running with a desktop environment, you can omit `--keywords` to enter them via GUI prompt.