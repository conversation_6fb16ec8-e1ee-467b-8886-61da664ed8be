import requests
from bs4 import BeautifulSoup

# GUI imports guarded for headless environments
try:
    import tkinter as tk
    from tkinter import simpledialog
except Exception:
    tk = None
    simpledialog = None

from requests.auth import HTTPBasicAuth
import concurrent.futures
import pyperclip
import argparse
import os
import sys

# 获取用户输入的关键词

def get_keywords():
    if tk is None or simpledialog is None:
        raise RuntimeError("当前环境不支持 GUI 输入，请使用 --keywords 提供关键词")
    root = tk.Tk()
    root.withdraw()
    keywords = simpledialog.askstring("输入", "请输入查询关键词，多个关键词使用空格分开")
    return (keywords or "").split()

# 发出查询请求并获取结果

def get_search_result(keyword):
    if keyword:  # 过滤掉空值
        url = f"https://everything.19870823.xyz/?search={keyword}+mkv|mp4|wmv"
        response = requests.get(url, auth=HTTPBasicAuth('fz19870823', 'admin.001'))
        soup = BeautifulSoup(response.text, 'html.parser')
        result_tag = soup.find('p', class_='numresults')
        if result_tag:
            result = int(result_tag.text.split()[0])
            if result > 0:
                return (keyword, result, True)
            else:
                return (keyword, result, False)
    return (keyword, 0, False)

# 主函数

def searchlocal(keywords=None, no_clipboard: bool = False):
    results_greater_than_zero = []
    results_zero = []
    with concurrent.futures.ThreadPoolExecutor(max_workers=15) as executor:
        future_to_keyword = {executor.submit(get_search_result, keyword): keyword for keyword in keywords}
        for future in concurrent.futures.as_completed(future_to_keyword):
            keyword, result, is_greater_than_zero = future.result()
            if is_greater_than_zero:
                results_greater_than_zero.append((keyword, result))
            else:
                results_zero.append((keyword, result))
    print("结果大于0的关键词和结果数：", results_greater_than_zero)
    print("结果为0的关键词和结果数：", results_zero)
    zero_keywords = ' '.join(keyword for keyword, result in results_zero)
    print("结果为0的关键词（用空格相连）：", zero_keywords)
    if not no_clipboard:
        try:
            pyperclip.copy(zero_keywords)
            print("结果为0的关键词已复制到剪贴板")
        except Exception as e:
            print(f"复制到剪贴板失败：{e}")


# 如果直接运行这个文件，就执行 main 函数
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="本地 Everything 网页搜索工具")
    parser.add_argument('--keywords', nargs='*', help='直接提供关键词，空格分隔')
    parser.add_argument('--no-clipboard', action='store_true', help='不复制结果到剪贴板')
    args = parser.parse_args()

    use_gui = (args.keywords is None) and (tk is not None) and (os.environ.get("DISPLAY") or sys.platform.startswith("win") or sys.platform == "darwin")
    if args.keywords is None:
        if use_gui:
            keywords = get_keywords()
        else:
            print('缺少关键词，请通过 --keywords 提供，或在有图形界面下运行。')
            sys.exit(1)
    else:
        keywords = args.keywords

    searchlocal(keywords, no_clipboard=args.no_clipboard)
    # 无需等待用户输入，兼容非交互环境