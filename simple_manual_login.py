#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的手动登录测试脚本
"""

import tempfile
import time
import undetected_chromedriver as uc
from selenium.webdriver.chrome.options import Options as ChromeOptions
from get_actors import extract_actor_urls, get_next_page_url, save_actor_urls_to_file
from javdbid import get_page


def simple_manual_login_test():
    """简单的手动登录测试"""
    
    # 创建Chrome选项
    options = ChromeOptions()
    options.add_argument("--disable-gpu")
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-blink-features=AutomationControlled")
    
    # 创建临时用户数据目录
    temp_user_data = tempfile.mkdtemp(prefix="chrome_test_")
    options.add_argument(f"--user-data-dir={temp_user_data}")
    
    driver = None
    try:
        print("正在启动Chrome浏览器...")
        driver = uc.Chrome(options=options)
        print("浏览器启动成功")
        
        # 打开JAVDB主页
        print("正在打开JAVDB主页...")
        driver.get("https://javdb.com")
        
        print("\n" + "="*60)
        print("请在浏览器中完成以下操作:")
        print("1. 点击登录按钮")
        print("2. 输入用户名和密码")
        print("3. 完成登录")
        print("4. 手动导航到演员收藏页面: https://javdb.com/users/collection_actors")
        print("5. 确认页面加载完成后，按回车键继续...")
        print("="*60)
        
        # 等待用户完成登录和导航
        input("完成登录和导航后请按回车键继续...")
        
        # 获取当前页面内容
        print("正在获取页面内容...")
        current_url = driver.current_url
        print(f"当前页面URL: {current_url}")
        
        # 使用BeautifulSoup解析页面
        from bs4 import BeautifulSoup
        page_source = driver.page_source
        soup = BeautifulSoup(page_source, 'html.parser')
        
        # 保存页面用于调试
        with open("manual_login_page.html", 'w', encoding='utf-8') as f:
            f.write(soup.prettify())
        print("页面已保存到: manual_login_page.html")
        
        # 提取演员URL
        actor_urls = extract_actor_urls(soup)
        print(f"找到 {len(actor_urls)} 个演员链接")
        
        if actor_urls:
            # 保存结果
            save_actor_urls_to_file(list(actor_urls), "manual_actors.txt")
            
            # 显示前几个URL
            print("\n前10个演员URL:")
            for i, url in enumerate(list(actor_urls)[:10], 1):
                print(f"{i:2d}. {url}")
        else:
            print("没有找到演员链接")
            
            # 检查页面内容
            title = soup.find('title')
            if title:
                print(f"页面标题: {title.get_text()}")
            
            # 检查是否需要登录
            page_text = soup.get_text()
            if "登入" in page_text or "login" in page_text.lower():
                print("页面仍然显示需要登录")
            else:
                print("页面不显示登录要求，可能是其他问题")
    
    except Exception as e:
        print(f"测试过程中出错: {e}")
    
    finally:
        if driver:
            print("正在关闭浏览器...")
            driver.quit()


if __name__ == "__main__":
    simple_manual_login_test()
